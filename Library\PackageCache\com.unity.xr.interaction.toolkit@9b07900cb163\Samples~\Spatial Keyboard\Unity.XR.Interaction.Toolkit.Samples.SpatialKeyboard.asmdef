{"name": "Unity.XR.Interaction.Toolkit.Samples.SpatialKeyboard", "rootNamespace": "", "references": ["Unity.Mathematics", "Unity.TextMeshPro", "Unity.XR.CoreUtils", "Unity.XR.Interaction.Toolkit"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.textmeshpro", "expression": "3.0.8", "define": "TEXT_MESH_PRO_PRESENT"}, {"name": "com.unity.ugui", "expression": "2.0.0", "define": "UGUI_2_0_PRESENT"}], "noEngineReferences": false}