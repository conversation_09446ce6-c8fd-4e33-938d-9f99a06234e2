{"name": "Unity.XR.Interaction.Toolkit.Samples.VisionOS", "rootNamespace": "", "references": ["Unity.InputSystem", "Unity.Mathematics", "Unity.XR.CoreUtils", "Unity.XR.Interaction.Toolkit", "Unity.XR.ARFoundation", "Unity.PolySpatial", "Unity.PolySpatial.RealityKit", "Unity.PolySpatial.XR", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets", "Unity.TextMeshPro"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.polyspatial", "expression": "1.1.6", "define": "POLYSPATIAL_1_1_OR_NEWER"}], "noEngineReferences": false}