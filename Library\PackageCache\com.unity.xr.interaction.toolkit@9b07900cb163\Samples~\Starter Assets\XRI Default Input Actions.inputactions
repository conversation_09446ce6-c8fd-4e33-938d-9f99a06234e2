{"name": "XRI Default Input Actions", "maps": [{"name": "XRI Head", "id": "09ff3ccc-21b4-4346-a3a2-7c978b5af892", "actions": [{"name": "Position", "type": "Value", "id": "1a9029f8-7a46-46b9-9eff-e9ae8365f611", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotation", "type": "Value", "id": "aed87fe6-2b01-4dd2-a8fa-195578fd8158", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Is Tracked", "type": "<PERSON><PERSON>", "id": "6bb4e248-e42b-47c3-b66c-79566508ca74", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Tracking State", "type": "Value", "id": "08654a17-c094-4bbd-8946-415ae4ce2406", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Eye Gaze Position", "type": "Value", "id": "dde820a2-0462-4756-be47-630b5b56c115", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Eye Gaze Rotation", "type": "Value", "id": "8ac32629-4403-4068-aae5-2cd243e230c2", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Eye Gaze Is Tracked", "type": "<PERSON><PERSON>", "id": "ea26ba43-844b-4585-817a-2f124b571813", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Eye Gaze Tracking State", "type": "Value", "id": "73053154-2fbc-4d78-9cac-000282b64f79", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "cff1f981-6e1f-4e2c-a90c-715a0ea2e80e", "path": "<XRHMD>/centerEyePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4d22c160-9642-4784-bed3-f108d9099185", "path": "<HandheldARInputDevice>/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e2017383-a3f6-4c46-acb1-012b8eece9cc", "path": "<XRHMD>/centerEyeRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "30a88e64-475a-4a1c-aca0-80b6a2bc3327", "path": "<HandheldARInputDevice>/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "Quaternion Fallback", "id": "fd9bd2d1-a464-4069-bf55-7f7a3cdb5a96", "path": "QuaternionFallback", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "ef9a3bee-0af0-4688-81d3-49c2d9be0def", "path": "<EyeGaze>/pose/rotation", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "aaf13e7c-a814-4c6e-9349-042da0cb27e9", "path": "<XRHMD>/centerEyeRotation", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "3e829ba4-2fad-45ea-8114-7670f0e484be", "path": "", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "Vector 3 Fallback", "id": "0cf0b092-6006-474b-9cf5-dc4039450f39", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "872dc9a3-bab9-4b3f-9f84-8d12371f1f67", "path": "<EyeGaze>/pose/position", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "2f870b88-8825-4a62-b02e-b5a523723446", "path": "<XRHMD>/centerEyePosition", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "eaececb1-b4a3-4b47-83c2-60562a364085", "path": "", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Position", "isComposite": false, "isPartOfComposite": true}, {"name": "Integer Fallback", "id": "1a6685cf-ae82-4f22-a967-75610a8e71ed", "path": "IntegerFallback", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Tracking State", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "0dc9d652-871d-4ba5-94a1-50cf8218009a", "path": "<EyeGaze>/pose/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Tracking State", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "9bbd034f-9254-4dd5-9df7-d84f53b0bc8d", "path": "<XRHMD>/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Tracking State", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "d5e35a2f-03c4-432a-8e5e-d200278bf0a9", "path": "", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Tracking State", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "43a7b1a1-e99a-4346-a058-5b68c535729d", "path": "<XRHMD>/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Tracking State", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "15eff92f-dac7-4e6e-986c-08b3f1e73fac", "path": "<XRHMD>/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Is Tracked", "isComposite": false, "isPartOfComposite": false}, {"name": "Button Fallback", "id": "c262df21-2ffb-4295-93cc-8fdb5649da7e", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Is Tracked", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "f77492f8-09ee-49d0-b821-7c31cb5c2a16", "path": "<EyeGaze>/pose/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Is Tracked", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "960252ae-88e8-427a-ac9f-ecac6fb3c7d1", "path": "<XRHMD>/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Is Tracked", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "dfc94c22-a115-471f-9f17-8cb6bc7f6637", "path": "", "interactions": "", "processors": "", "groups": "", "action": "Eye Gaze Is Tracked", "isComposite": false, "isPartOfComposite": true}]}, {"name": "XRI Left", "id": "5fe596f9-1b7b-49b7-80a7-3b5195caf74d", "actions": [{"name": "Position", "type": "Value", "id": "83a7af0b-87e3-42c3-a909-95fbf8091e4f", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotation", "type": "Value", "id": "cb6b7130-2bac-4ef7-abe4-6991ae7d419d", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Is Tracked", "type": "<PERSON><PERSON>", "id": "82eb6741-beef-48d3-83ab-a957dc1caa1e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Tracking State", "type": "Value", "id": "d20fc51c-7916-43a7-8b03-706049966aea", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Haptic Device", "type": "PassThrough", "id": "664a62b0-e178-421d-b3f8-014eec01591d", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Aim Position", "type": "Value", "id": "c73a0160-3d9b-4dde-96f9-6a390e68778c", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Aim Rotation", "type": "Value", "id": "f208faac-e869-4280-ac9c-9b3d0ab819bb", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Meta Aim Flags", "type": "Value", "id": "f98e71db-49b4-4882-8991-a0e386733e87", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Pinch Position", "type": "Value", "id": "cac52a91-5970-4ad2-8c86-a8c0e91a1837", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Poke Position", "type": "Value", "id": "4c557d81-3795-4355-a83e-6f886221d011", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Poke Rotation", "type": "Value", "id": "0565b7f7-f841-4395-98df-a77f4dd6d9c9", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grip Position", "type": "Value", "id": "e1240870-ef45-4f3e-8110-ff1b9049c4ca", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grip Rotation", "type": "Value", "id": "41873a55-b316-4dbe-96e6-93477eef5e47", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Thumbstick", "type": "Value", "id": "c01850c4-700b-4ae6-a187-a894afef5bbd", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "Quaternion Fallback", "id": "61466a56-4ee4-47b1-aa6a-4806de1de5f2", "path": "QuaternionFallback", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "afdcfbff-e241-4fdd-a6d1-23b0bf273360", "path": "<XRController>{LeftHand}/pointerRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "ed03d944-4c09-4c38-8b68-5c844e18ca7c", "path": "<XRController>{LeftHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "c98fc8c8-7fc6-4909-89b6-c5b7568e7275", "path": "<XRHandDevice>{LeftHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "Vector 3 Fallback", "id": "14aeff85-d719-43ff-a124-b1cd7ca8686d", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "abf752ec-feee-4d51-b530-f0870f48acc9", "path": "<XRController>{LeftHand}/pointerPosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "6580b669-0651-401c-9779-85ef22689130", "path": "<XRController>{LeftHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "ae101942-9eaa-4c53-a388-cafc3fd89bdf", "path": "<XRHandDevice>{LeftHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "acdf9550-5529-4ff7-8558-73ecdf0d75bd", "path": "<XRController>{LeftHand}/*", "interactions": "", "processors": "", "groups": "", "action": "Haptic Device", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "97a0351f-659b-482a-8fa0-19015ccd055e", "path": "<XRController>{LeftHand}/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Tracking State", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "34454fec-7610-497a-b1a5-d3d5f01b312c", "path": "<XRHandDevice>{LeftHand}/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Tracking State", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "28df8d2f-b563-4377-bd11-6c8932ee591c", "path": "<MetaAimHand>{LeftHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8ee39b25-fde6-4195-bc6e-68caadef9183", "path": "<HandInteraction>{LeftHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ec4a1046-3843-445d-8ad4-a769823faa86", "path": "<HandInteractionPoses>{LeftHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d29757d4-ec35-4477-8a26-1d14acd14ba9", "path": "<HoloLensHand>{LeftHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2623b909-75bd-40da-97bd-ae1ecfb0a89b", "path": "<MetaAimHand>{LeftHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c3ff2c3c-d1ef-40c9-8777-72ee03df3ff3", "path": "<HandInteraction>{LeftHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "68633061-addf-447b-969e-06249302eaad", "path": "<HandInteractionPoses>{LeftHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4848899c-8c90-455a-a915-6422290f501b", "path": "<HoloLensHand>{LeftHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "Vector 3 Fallback", "id": "aff6849b-544f-473c-9d7e-da40488aa6ab", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "e62f5918-8922-4a59-ae42-179b1fde0d29", "path": "<XRHandDevice>{LeftHand}/pinchPosition", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "1e9cdc2b-24e6-4624-9bd8-02c7dcf68fb4", "path": "<HandInteraction>{LeftHand}/pinchPose/position", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "0adea6d4-f14c-4243-8217-dd63b6529bcc", "path": "<HandInteractionPoses>{LeftHand}/pinchPose/position", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "Vector 3 Fallback", "id": "2ef93166-d4e5-471d-8321-71e7cdec9220", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "55020194-7022-4059-8424-8ecc0de92c13", "path": "<XRHandDevice>{LeftHand}/pokePosition", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "9a8820cc-2172-4641-9fae-0c416b2649e2", "path": "<HandInteraction>{LeftHand}/pokePose/position", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "ef0a4717-47f8-47a3-8dae-fd0cba366115", "path": "<HandInteractionPoses>{LeftHand}/pokePose/position", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "Quaternion Fallback", "id": "892e5765-05a7-460a-942c-32e8a36bd441", "path": "QuaternionFallback", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "5f5fb46a-effc-4105-bb74-d30dc9cd1f43", "path": "<XRHandDevice>{LeftHand}/pokeRotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "8fba8372-e2dd-4a31-8048-45d49484323e", "path": "<HandInteraction>{LeftHand}/pokePose/rotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "8c1d4e9b-88f5-4966-a6e4-0f22f7bb896d", "path": "<HandInteractionPoses>{LeftHand}/pokePose/rotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "f1de7b81-80d1-4207-8f19-4fb96a537bb3", "path": "<XRController>{LeftHand}/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Is Tracked", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d9f33fe3-b3bf-48c1-a8bc-dd6a4ddfba94", "path": "<XRHandDevice>{LeftHand}/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Is Tracked", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "286f44f6-74a5-4f92-8468-42445c7a3cb8", "path": "<MetaAimHand>{LeftHand}/aimFlags", "interactions": "", "processors": "", "groups": "", "action": "Meta Aim Flags", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "21a4b6f9-1ebe-434f-b572-066a77b04c48", "path": "<XRHandDevice>{LeftHand}/gripPosition", "interactions": "", "processors": "", "groups": "", "action": "Grip Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3c1f9e74-aba4-41d6-b519-90ed563be5d7", "path": "<XRHandDevice>{LeftHand}/gripRotation", "interactions": "", "processors": "", "groups": "", "action": "Grip Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0123e5fd-aec0-4ab3-b201-2e6c65d2b93e", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "", "action": "Thumbstick", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI Left Interaction", "id": "7a5e7537-cc30-4eb1-a544-6946baa8f3eb", "actions": [{"name": "Select", "type": "<PERSON><PERSON>", "id": "33754c03-48ec-46ef-9bc6-22ed6bfdd8e8", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Select Value", "type": "Value", "id": "e6005f29-e4c1-4f3b-8bf7-3a28bab5ca9c", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Activate", "type": "<PERSON><PERSON>", "id": "0c0991c5-d329-4afc-8892-1076b440477c", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Activate Value", "type": "Value", "id": "0c3d0ec9-85a1-45b3-839b-1ca43f859ecd", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "UI Press", "type": "<PERSON><PERSON>", "id": "7e1eced7-c774-4fe5-be8f-d8711f646d9e", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "UI Press Value", "type": "Value", "id": "f241c1aa-1050-4338-b2bf-a4a47776693d", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "UI Scroll", "type": "Value", "id": "a5372626-7022-4ba7-b152-6f26318fd8a8", "expectedControlType": "Vector2", "processors": "InvertVector2(invertY=false)", "interactions": "", "initialStateCheck": true}, {"name": "Translate Manipulation", "type": "Value", "id": "bfa204c7-3c92-4193-bad1-39eb71920042", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotate Manipulation", "type": "Value", "id": "21b75b25-12ad-410f-b4f8-a7745b7aca27", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Manipulation", "type": "Value", "id": "93bd97c5-fd23-4853-8045-1b12324aa24e", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Scale Toggle", "type": "<PERSON><PERSON>", "id": "80ed7d74-56de-473c-bf76-da3bdd16b562", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Scale Over Time", "type": "Value", "id": "2257500c-1efb-4f69-a54d-ed5db2708616", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "71a4d23f-3e9a-4513-923b-ba388c5e84bf", "path": "<XRController>{LeftHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "73325635-d9e5-481a-9279-ae7be089422d", "path": "<MetaAimHand>{LeftHand}/indexPressed", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3bbf9f24-2edd-41b9-8456-683298f1e58c", "path": "<HandInteraction>{LeftHand}/graspFirm", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a86585c7-1d41-40e2-a7ca-bb76cca5c32a", "path": "<HandInteraction>{LeftHand}/pinchTouched", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ebab6345-d4f7-4a42-94b3-12d4464de218", "path": "<HoloLensHand>{LeftHand}/squeezePressed", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "87d9a1e7-704b-43bb-be2a-f2d8ecfde8b7", "path": "<KHRSimpleController>{LeftHand}/select", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "49a23327-a116-48c0-8af9-0d2c50c15a88", "path": "<XRController>{LeftHand}/{Grip}", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "304be843-4b23-45d9-89fa-005ac163d9b9", "path": "<MetaAimHand>{LeftHand}/pinchStrengthIndex", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2a4b758a-252a-484c-9a26-438954189c08", "path": "<HandInteraction>{LeftHand}/graspValue", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bc272ed6-7655-4292-9c21-e5b87bec4350", "path": "<HandInteraction>{LeftHand}/pinchValue", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a8d99ae3-e736-4370-ad5e-9fa45cb7a1be", "path": "<HoloLensHand>{LeftHand}/squeeze", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "487f4f2e-9e9b-49aa-b0f2-4037a24624f5", "path": "<XRController>{LeftHand}/{TriggerButton}", "interactions": "", "processors": "", "groups": "", "action": "Activate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3b8b1b59-2fdc-4998-8259-50341075d9a2", "path": "<XRController>{LeftHand}/{Trigger}", "interactions": "", "processors": "", "groups": "", "action": "Activate Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b34c79c1-ab5e-4851-87ac-abc43705eae0", "path": "<XRController>{LeftHand}/{TriggerButton}", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a5bf6a12-a026-46d1-a793-7252c49aaf66", "path": "<MetaAimHand>{LeftHand}/indexPressed", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fc8b2287-429e-4be4-a34b-cca7c50eeb52", "path": "<HandInteraction>{LeftHand}/pointerActivated", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ca931b95-39e0-4db1-9887-f5a5f68298d4", "path": "<HoloLensHand>{LeftHand}/selectPressed", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "36eadd9a-281d-47fe-9d16-5870d6e00480", "path": "<KHRSimpleController>{LeftHand}/select", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "71d94579-1bf4-4034-ab9e-e7166842128f", "path": "<XRController>{LeftHand}/{Trigger}", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d250c9be-4bf2-4b5c-8962-4fcf5d53bdb3", "path": "<MetaAimHand>{LeftHand}/pinchStrengthIndex", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c4c2fc93-75fe-4910-95ca-6b1cc163a48a", "path": "<HandInteraction>{LeftHand}/pointerActivateValue", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c7b5ae56-e532-43a3-a053-8d66df507df1", "path": "<HoloLensHand>{LeftHand}/select", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8ed313a6-c966-4669-8a62-4bb2319d485b", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(y=0),StickDeadzone", "groups": "", "action": "Rotate Manipulation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7b30ca4b-9f98-4a44-9af5-a89412d5cdc8", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "", "action": "UI Scroll", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8f872fc5-75dc-49e5-9bbd-f2e4d4498c65", "path": "<XRController>{LeftHand}/{Primary2DAxisClick}", "interactions": "", "processors": "", "groups": "", "action": "Scale Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b21d690b-51fc-413f-a887-08a2a39af3fc", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(x=0),StickDeadzone", "groups": "", "action": "Scale Over Time", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5fe0ed53-b4d3-4cd8-b567-397a7d1e1c6a", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "Manipulation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "370f21e3-a80b-4b07-990b-299c2da0929a", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(x=0),StickDeadzone", "groups": "", "action": "Translate Manipulation", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI Left Locomotion", "id": "22336389-9fb1-4c2c-8635-0ed30db0d29e", "actions": [{"name": "Teleport Mode", "type": "Value", "id": "a21db72c-4843-4839-b4d0-3ce8d287cb86", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Teleport Mode Cancel", "type": "<PERSON><PERSON>", "id": "89ce8348-6001-41a3-85b9-f8f2e2dcad7c", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Turn", "type": "Value", "id": "9164e093-ebd4-4923-af32-1b52f31c2d66", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Snap Turn", "type": "Value", "id": "8c14e969-a054-4f12-840c-4e0bd85173d9", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Move", "type": "Value", "id": "9693e25f-8a4f-4aed-842f-3961243c69a1", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grab Move", "type": "<PERSON><PERSON>", "id": "c5a6d766-d487-42ae-b293-da4749469e18", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "8da6ed3a-f621-49fe-8c76-1f6b7d7754d6", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "Sector(directions=1)", "processors": "", "groups": "", "action": "Teleport Mode", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b8aebee7-fa03-43d4-bfb7-77a3f87452cc", "path": "<XRController>{LeftHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Teleport Mode Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "99cb7ad1-51ec-4611-af68-92a85f2c17d6", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "Sector(directions=12,sweepBehavior=1),Sector(directions=2,sweepBehavior=2)", "processors": "", "groups": "", "action": "Turn", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8e383b1a-270f-4c20-819b-89a59cffb498", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9e2fa814-8cbd-4c65-a60d-a1503f30ffd8", "path": "<XRController>{LeftHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Grab Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a46b49d0-0754-4dac-a9a5-a822e10751f2", "path": "<XRController>{LeftHand}/{Primary2DAxis}", "interactions": "Sector(directions=12,sweepBehavior=1),Sector(directions=2,sweepBehavior=2)", "processors": "", "groups": "", "action": "Snap Turn", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI Right", "id": "7960f8ef-2bf3-4281-aecc-4c03809d6c8c", "actions": [{"name": "Position", "type": "Value", "id": "c4990d70-7b8a-4ce1-b03c-da86716b8352", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotation", "type": "Value", "id": "ee6bf5bf-bb0a-4a50-8327-cb654b19e298", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Is Tracked", "type": "<PERSON><PERSON>", "id": "a705ffe4-b2c8-4b78-847f-25257d4e30af", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Tracking State", "type": "Value", "id": "167ea203-5bfb-4d74-bde9-8026b7483102", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Haptic Device", "type": "PassThrough", "id": "57b2a1b4-3290-46d6-ac07-4854ee8f91b1", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Aim Position", "type": "Value", "id": "daf49d5d-4ba8-4bf7-9010-e7cae2096907", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Aim Rotation", "type": "Value", "id": "148c182f-63ef-4709-8057-f6ea8070cb5c", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Meta Aim Flags", "type": "Value", "id": "93a75a21-033e-440c-9954-ff264afb2db9", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Pinch Position", "type": "Value", "id": "7a2e5dcd-3e49-4622-90ea-6607994f2be0", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Poke Position", "type": "Value", "id": "496d56bd-afd7-495b-a326-16e4ef742bc1", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Poke Rotation", "type": "Value", "id": "3767652c-5427-421b-8f8d-660106453cb1", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grip Position", "type": "Value", "id": "defe4495-ba8f-4958-b2fb-98d889e45ac5", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grip Rotation", "type": "Value", "id": "6bc56065-b0db-4265-8cef-5c7d4f40128a", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Thumbstick", "type": "Value", "id": "b8c0ccd3-e1b6-4913-96b3-e0864c9ac6bd", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "Quaternion Fallback", "id": "84e51e1c-1b95-4f3e-a61f-29da6c1f0816", "path": "QuaternionFallback", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "3722d501-eb80-4f61-9361-08a5ea7a1394", "path": "<XRController>{RightHand}/pointerRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "2e6ad191-d5aa-4919-aac6-295c83387a72", "path": "<XRController>{RightHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "b9ecb60d-341e-47cf-b50a-41d5815af8b0", "path": "<XRHandDevice>{RightHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "Vector 3 Fallback", "id": "74e968f1-ad08-4a82-a68d-764517faecef", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "9717e367-64a4-440a-9974-1e641d753eb2", "path": "<XRController>{RightHand}/pointerPosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "0794a41d-29ef-48ec-a452-6b7de29b52fa", "path": "<XRController>{RightHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "3ef0a781-60c5-48bc-a584-f95553f8ae0a", "path": "<XRHandDevice>{RightHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Position", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "6011e1e6-b2dd-4cb1-8da5-29b03868f2c5", "path": "<XRController>{RightHand}/*", "interactions": "", "processors": "", "groups": "", "action": "Haptic Device", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "03ccbaec-eeca-4fc4-8281-ee1758b4eb9b", "path": "<XRController>{RightHand}/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Tracking State", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "714d1173-f908-4bca-951c-4adb4eb7b4c5", "path": "<XRHandDevice>{RightHand}/trackingState", "interactions": "", "processors": "", "groups": "", "action": "Tracking State", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "dd822fc8-c655-4a4d-87d0-9575760b6dca", "path": "<MetaAimHand>{RightHand}/devicePosition", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e60d7767-705f-4af2-ae42-f135e6580630", "path": "<HandInteraction>{RightHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d34dafcf-a3a4-4511-a73f-1ecbfd6099c8", "path": "<HandInteractionPoses>{RightHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "720d5e83-6877-4504-9b4a-aa550c2593af", "path": "<HoloLensHand>{RightHand}/pointer/position", "interactions": "", "processors": "", "groups": "", "action": "Aim Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2bb1158a-2d78-446b-9351-6f9b3f1364cb", "path": "<MetaAimHand>{RightHand}/deviceRotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "256fbef4-95a6-4127-ac3e-6a259b640666", "path": "<HandInteraction>{RightHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d46291b9-775e-457b-a909-649a301d55c3", "path": "<HandInteractionPoses>{RightHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2fac2635-e015-4fb7-9578-34b85a5d5797", "path": "<HoloLensHand>{RightHand}/pointer/rotation", "interactions": "", "processors": "", "groups": "", "action": "Aim Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "Vector 3 Fallback", "id": "0323576b-ec88-4459-a791-4afeada3f7c8", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "0bdf98f1-d1a8-443f-805e-9718b34fc6ea", "path": "<XRHandDevice>{RightHand}/pinchPosition", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "0bd62ec6-1259-40f6-aa0a-71b82a790764", "path": "<HandInteraction>{RightHand}/pinchPose/position", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "7271c456-534a-4e51-9835-1ebd589a938e", "path": "<HandInteractionPoses>{RightHand}/pinchPose/position", "interactions": "", "processors": "", "groups": "", "action": "Pinch Position", "isComposite": false, "isPartOfComposite": true}, {"name": "Vector 3 Fallback", "id": "5724159b-b0ee-4458-b567-63874ee6e24a", "path": "Vector3Fallback", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "3f5ff135-3cfa-48b6-a35c-aa52badc1d6e", "path": "<XRHandDevice>{RightHand}/pokePosition", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "c46e492c-1618-4d10-8c99-3079cf9deda6", "path": "<HandInteraction>{RightHand}/pokePose/position", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "34b9f870-767e-422c-b558-0708567a1a5d", "path": "<HandInteractionPoses>{RightHand}/pokePose/position", "interactions": "", "processors": "", "groups": "", "action": "Poke Position", "isComposite": false, "isPartOfComposite": true}, {"name": "Quaternion Fallback", "id": "74a5f1ad-f8ed-42cf-aff3-eb911325ca7d", "path": "QuaternionFallback", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "first", "id": "595c1ccb-9c05-411a-a2fd-e892ca0c9091", "path": "<XRHandDevice>{RightHand}/pokeRotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "second", "id": "d7ce6129-ec94-4757-9595-aaf1032cae86", "path": "<HandInteraction>{RightHand}/pokePose/rotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "third", "id": "0e7b9607-caf5-46cc-adb9-2a1500c718a4", "path": "<HandInteractionPoses>{RightHand}/pokePose/rotation", "interactions": "", "processors": "", "groups": "", "action": "Poke Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "1de48aee-890b-4dbb-a02d-51df9bd39db7", "path": "<XRController>{RightHand}/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Is Tracked", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5a0c4fe0-639a-44e0-beeb-4e11e0dea7ef", "path": "<XRHandDevice>{RightHand}/isTracked", "interactions": "", "processors": "", "groups": "", "action": "Is Tracked", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5469d4d1-9645-4397-a596-d74f876eafc2", "path": "<MetaAimHand>{RightHand}/aimFlags", "interactions": "", "processors": "", "groups": "", "action": "Meta Aim Flags", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "efcaf94e-8faa-439e-983b-c65f79c3b743", "path": "<XRHandDevice>{RightHand}/gripPosition", "interactions": "", "processors": "", "groups": "", "action": "Grip Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "70a747b6-46e8-4d3b-aaec-20bcab8f1dd7", "path": "<XRHandDevice>{RightHand}/gripRotation", "interactions": "", "processors": "", "groups": "", "action": "Grip Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "709cc921-f3b0-4dc8-88d4-7787b8a3ced1", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "", "action": "Thumbstick", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI Right Interaction", "id": "461bce25-7762-40c5-b639-f190649be6d6", "actions": [{"name": "Select", "type": "<PERSON><PERSON>", "id": "ac96c10b-c955-4a46-8e67-bf16bc069b53", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Select Value", "type": "Value", "id": "39bbf1ac-21a3-413d-90f6-6dbf6efeaabe", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Activate", "type": "<PERSON><PERSON>", "id": "41976d89-60de-4deb-bff9-16b4af96b290", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Activate Value", "type": "Value", "id": "c3ca6ed7-3d25-44a2-b1d8-5be4eb699370", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "UI Press", "type": "<PERSON><PERSON>", "id": "65174b45-c2ee-4f90-93bb-fb4084eaaab3", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "UI Press Value", "type": "Value", "id": "962ac033-ec42-4981-88a4-551ad9be6ecb", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "UI Scroll", "type": "Value", "id": "c283b939-751f-426e-8462-142a529993e3", "expectedControlType": "Vector2", "processors": "InvertVector2(invertY=false)", "interactions": "", "initialStateCheck": true}, {"name": "Translate Manipulation", "type": "Value", "id": "6f7cf253-7062-443b-b10f-2be48a33f027", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotate Manipulation", "type": "Value", "id": "9b5d8312-f609-4895-b70f-81a722b2ae11", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Manipulation", "type": "Value", "id": "b950a329-6492-4e29-b563-afc726f81e95", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Scale Toggle", "type": "<PERSON><PERSON>", "id": "5ad73d15-99a4-4bce-a76f-f49815602416", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Scale Over Time", "type": "Value", "id": "19a21f59-bd21-4f77-b29d-4fda26ef6769", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "1ce80054-410d-4112-a332-50faa7fb4f23", "path": "<XRController>{RightHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1d5b47ea-64e3-4b99-b620-de6c360908be", "path": "<MetaAimHand>{RightHand}/indexPressed", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1fe3ab58-51f3-4274-995c-176ac72d9610", "path": "<HandInteraction>{RightHand}/graspFirm", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f6083118-4e38-45a2-afaf-52fa60444f78", "path": "<HandInteraction>{RightHand}/pinchTouched", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "82f232f1-6246-4d1e-aacc-a7ccc16c76d9", "path": "<HoloLensHand>{RightHand}/squeezePressed", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "69580c32-8c9a-47d9-8b23-b53d760ca5da", "path": "<KHRSimpleController>{RightHand}/select", "interactions": "", "processors": "", "groups": "", "action": "Select", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "dd433817-216c-46b9-8dd3-f3a4ea1767b9", "path": "<XRController>{RightHand}/{Grip}", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "770a07b5-a199-4342-b4a5-b3baafbe2bcb", "path": "<MetaAimHand>{RightHand}/pinchStrengthIndex", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e8d22d4b-ac0c-452b-9f5e-247f94754302", "path": "<HandInteraction>{RightHand}/graspValue", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c850e784-816f-4df7-8759-a725cb4a84bf", "path": "<HandInteraction>{RightHand}/pinchValue", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e890a130-d436-4b52-a092-bff81d18bfb7", "path": "<HoloLensHand>{RightHand}/squeeze", "interactions": "", "processors": "", "groups": "", "action": "Select Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fa59aed1-ae0b-4074-a58c-294b85f46228", "path": "<XRController>{RightHand}/{TriggerButton}", "interactions": "", "processors": "", "groups": "", "action": "Activate", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "150f414f-61bf-47b1-b4f8-f772a2a40565", "path": "<XRController>{RightHand}/{Trigger}", "interactions": "", "processors": "", "groups": "", "action": "Activate Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "92bb5b8f-bf48-4dab-af05-50a865773895", "path": "<XRController>{RightHand}/{TriggerButton}", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1652c26e-d835-461f-b46b-55b146fd9bba", "path": "<MetaAimHand>{RightHand}/indexPressed", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7ae41e93-9c2b-4d15-8387-0eddbc823053", "path": "<HandInteraction>{RightHand}/pointerActivated", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f8a900c7-8116-4f44-9d24-8f19caf07108", "path": "<HoloLensHand>{RightHand}/selectPressed", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a65008a0-f3b6-4060-b3a0-3eabaf0563bf", "path": "<KHRSimpleController>{RightHand}/select", "interactions": "", "processors": "", "groups": "", "action": "UI Press", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5b4ef08d-9ddd-4f0a-8539-d1114d14d143", "path": "<XRController>{RightHand}/{Trigger}", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "620cd3c3-a8c2-4a24-825a-ef6eb1cb41ef", "path": "<MetaAimHand>{RightHand}/pinchStrengthIndex", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4bfac4d7-1bce-4fa7-a6b1-00eb7e5f346e", "path": "<HandInteraction>{RightHand}/pointerActivateValue", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e2fbf204-5031-483f-beaa-abf05113dbc7", "path": "<HoloLensHand>{RightHand}/select", "interactions": "", "processors": "", "groups": "", "action": "UI Press Value", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5c0fa06c-b670-477f-a95d-eb3b4880e439", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(y=0),StickDeadzone", "groups": "", "action": "Rotate Manipulation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "af6fd279-2f48-4f51-8e9d-29b0b9d926f8", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "", "action": "UI Scroll", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d8258e49-f7cc-44d7-bc27-77c2161e2005", "path": "<XRController>{RightHand}/{Primary2DAxisClick}", "interactions": "", "processors": "", "groups": "", "action": "Scale Toggle", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ef10c39a-2987-41bb-bb80-0e476240adaa", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(x=0),StickDeadzone", "groups": "", "action": "Scale Over Time", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c4b46d7d-8231-4672-83f9-75af565faf57", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "Manipulation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4caf4e8d-13e5-4bd6-8f42-b6b99c315ad0", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "ScaleVector2(x=0),StickDeadzone", "groups": "", "action": "Translate Manipulation", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI Right Locomotion", "id": "99ce76d3-82c5-4289-9670-2ecffa6833fd", "actions": [{"name": "Teleport Mode", "type": "Value", "id": "a6c7231d-c55d-4dd4-9e87-877bb5522ef5", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Teleport Mode Cancel", "type": "<PERSON><PERSON>", "id": "d587b60c-39a0-4365-8075-477ce484ba0f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Turn", "type": "Value", "id": "9fb2eb2b-2fb6-4328-8167-10a1bf11b424", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Snap Turn", "type": "Value", "id": "44441ad6-5762-466d-ad54-aa44fcd61a5c", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Move", "type": "Value", "id": "00a4dc9f-1ee6-4349-b0e9-72d5dccaadd6", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Grab Move", "type": "<PERSON><PERSON>", "id": "cfb29d37-3db0-4e5d-a73b-7d48a19e279e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "01c7fda4-9c15-4167-8b87-58024f21d903", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "62690862-4688-4010-975b-b3d9c6062157", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "Sector(directions=1)", "processors": "", "groups": "", "action": "Teleport Mode", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "de466e6e-12bf-46a1-b0fd-ffbc343f3399", "path": "<XRController>{RightHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Teleport Mode Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d6c08c3d-3d41-4695-994d-1ac9016a5a9e", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "Sector(directions=12,sweepBehavior=1),Sector(directions=2,sweepBehavior=2)", "processors": "", "groups": "", "action": "Turn", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "022046aa-be71-4288-859d-6dd42844f6e6", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7ecb549e-ab98-4a4b-b979-38068fe3b811", "path": "<XRController>{RightHand}/{GripButton}", "interactions": "", "processors": "", "groups": "", "action": "Grab Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "31d838df-4d4e-4c16-a373-b9c07d9d2e2a", "path": "<XRController>{RightHand}/{Primary2DAxis}", "interactions": "Sector(directions=12,sweepBehavior=1),Sector(directions=2,sweepBehavior=2)", "processors": "", "groups": "", "action": "Snap Turn", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "052b129b-fc83-4b33-a606-af10ad595c3e", "path": "<XRController>{RightHand}/{PrimaryButton}", "interactions": "", "processors": "", "groups": "", "action": "Jump", "isComposite": false, "isPartOfComposite": false}]}, {"name": "XRI UI", "id": "edd65a7c-601c-4915-8307-025a081d8790", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "c9a92aca-49d5-4910-8ade-8e994f0a31f0", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "eba98c2e-6268-4233-bb88-946287bc753c", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "448b396b-0885-4543-ac5a-8b3405da6791", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "682022c0-857a-4332-8753-7f8fcdf84d37", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "b194cd98-7e4f-457a-a60c-cebc25dc32a2", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "ScrollWheel", "type": "PassThrough", "id": "bd7fc534-75e3-489d-94fb-3d45cb78d8f3", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "cc5f5666-a75c-4dfc-8566-ded8ec9b4ae3", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightClick", "type": "PassThrough", "id": "533aeb95-18b2-4a83-a69d-f6e0be72ff8a", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "e87fa299-8441-4620-89dd-0564c7d552e2", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "445a013e-9c17-48a2-9856-067e4826df03", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "05c1e38e-79dd-41cb-95d5-74f42e65d92f", "path": "<Touchscreen>/position", "interactions": "", "processors": "", "groups": "", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "57909bb4-1088-4975-9227-ecc87a305257", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2740386d-d4b6-4342-903c-d9390783f04a", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": "", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d6fd3bb5-c747-4eba-b599-1c6d7c738e2a", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": "", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "fb2286cc-fa20-4564-bff6-9f790f12cf6b", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": "", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "54eb7da2-546a-4d75-bfcc-ae38be303a59", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "Gamepad", "id": "4c9a5170-d325-45ee-8ef9-fc12d1f5a97e", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "c846c708-b27e-4ac9-9a83-c80ac5c263d5", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "caf2fb01-3e95-47c1-8663-315057149d48", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "830d65e9-887d-45b4-8386-562deb29e465", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "e5c71442-9909-46d8-aa56-8fa3574a8227", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "210246e8-c3c4-4edc-be9c-1916858346df", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "fb701ef0-9910-4639-80d1-2c1c03f871ed", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "3de9f409-fb44-4311-8705-b4f4e7cd3029", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "bd001ba1-d6a2-4a97-9c87-36b5b92728af", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "fcc0a2cd-a126-43ad-bb1e-ffc1ae7668c7", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "65cbb13a-6e00-4973-9887-e49e06575091", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3c5803e2-42d0-4d48-bbd6-41ce4442df0b", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "d5b1219c-0df6-4bc5-ad11-205b748cade4", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "065d2394-f10a-46df-b6cb-2c56a6c842ea", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "29921809-7785-44a1-a316-e96307174552", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "49817cc8-fecc-406d-a187-6393de317e95", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "ef33431d-17d0-4e1c-90f2-bbaa2ef9a8b7", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "5ddfdce2-0f11-4f4e-8931-0ae6fb289ac7", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "5ceab4e0-1600-4bfb-acf6-8d02c4e10aea", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "94e10d8b-5bfa-439d-afae-b975efac2b7b", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "9fc7d14a-385d-4ca5-b185-906e049b7eed", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "d7e5e0c4-05dc-4f2f-8649-a66fe843caed", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "76ab9656-e168-4b2c-9a6b-d8d6da981e4f", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "ad7bc5b3-6ada-42a2-9cba-5c7334cba7be", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "9f2a7c29-a588-4b6a-a966-955eb408c526", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "67f51768-1493-4444-b118-82d398a16fdd", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "198e6869-709e-448d-96d4-27186c9d56e6", "path": "<Touchscreen>/Press", "interactions": "", "processors": "", "groups": "", "action": "Click", "isComposite": false, "isPartOfComposite": false}]}, {"name": "Touchscreen Gestures", "id": "6fb00339-a75a-4e5b-94e0-839f979f2a8a", "actions": [{"name": "Tap Start Position", "type": "Value", "id": "0f53f821-ec5d-472c-bd12-fb5ce515ae59", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Drag Start Position", "type": "Value", "id": "defa5165-8d03-4449-bdde-c0643730a763", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Drag Current Position", "type": "Value", "id": "07fd51be-2a34-4531-939c-ff750fcf8e4d", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Drag Delta", "type": "Value", "id": "ccd1d49f-8e5b-4c66-8d2c-fb774934270b", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Pinch Start Position", "type": "Value", "id": "07f4446a-0f0c-4176-a67e-75be05a3be3c", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Pinch Gap", "type": "Value", "id": "c299ab55-2420-4eb1-a459-0af3846471b9", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Pinch Gap Delta", "type": "Value", "id": "d1d816b2-4bec-4393-bf83-a59146ee0abc", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Twist Start Position", "type": "Value", "id": "e476e037-f414-4b6d-ac4a-486d7228ec43", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Twist Delta Rotation", "type": "Value", "id": "5910a9c0-4a90-4a2c-92cb-e33054cfd463", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Screen Touch Count", "type": "Value", "id": "c1e4b6b1-d82a-485c-9d29-9d42e48df255", "expectedControlType": "Integer", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Spawn Object", "type": "<PERSON><PERSON>", "id": "1415f3c5-fc5f-4f58-a044-4a69560151f2", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "fea81b99-07f5-426a-beba-5e0832c14855", "path": "<TouchscreenGestureInputController>/tapStartPosition", "interactions": "", "processors": "", "groups": "", "action": "Tap Start Position", "isComposite": false, "isPartOfComposite": false}, {"name": "One Modifier", "id": "ccaca70d-b804-4cda-9dd1-ee9152fa6ec8", "path": "OneModifier", "interactions": "Tap(duration=0.5)", "processors": "", "groups": "", "action": "Tap Start Position", "isComposite": true, "isPartOfComposite": false}, {"name": "modifier", "id": "30c845d9-0972-4e51-92bf-2eee8171abc7", "path": "<Mouse>/press", "interactions": "", "processors": "", "groups": "", "action": "Tap Start Position", "isComposite": false, "isPartOfComposite": true}, {"name": "binding", "id": "9ab23efd-1004-4423-b9b9-b070db6cde4e", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Tap Start Position", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "eb175a82-fad6-4249-bc9f-a6c9acee4436", "path": "<TouchscreenGestureInputController>/dragCurrentPosition", "interactions": "", "processors": "", "groups": "", "action": "Drag Current Position", "isComposite": false, "isPartOfComposite": false}, {"name": "One Modifier", "id": "2648faca-8c9b-4bcd-9653-fc9cfa39dbe3", "path": "OneModifier", "interactions": "", "processors": "", "groups": "", "action": "Drag Current Position", "isComposite": true, "isPartOfComposite": false}, {"name": "modifier", "id": "1f42a89b-32be-49d7-8153-507ff950cb3b", "path": "<Mouse>/press", "interactions": "", "processors": "", "groups": "", "action": "Drag Current Position", "isComposite": false, "isPartOfComposite": true}, {"name": "binding", "id": "7e88eed9-d5b6-4c3a-9dd7-ac83c45fced3", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Drag Current Position", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "f13815bd-33df-4670-9d5a-3295eafdda68", "path": "<TouchscreenGestureInputController>/dragStartPosition", "interactions": "", "processors": "", "groups": "", "action": "Drag Start Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "40fc89b1-2773-4288-b02f-892fea9b7d48", "path": "<TouchscreenGestureInputController>/twistDeltaRotation", "interactions": "", "processors": "", "groups": "", "action": "Twist Delta Rotation", "isComposite": false, "isPartOfComposite": false}, {"name": "1D Axis", "id": "57cc64a3-de05-45d9-971f-764c0aa9efe5", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Twist Delta Rotation", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "475bf595-e1da-44b6-8674-677260bd2dcf", "path": "<Keyboard>/r", "interactions": "", "processors": "", "groups": "", "action": "Twist Delta Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "05a801e2-b5df-4253-b3a9-ad2213853f57", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "", "action": "Twist Delta Rotation", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "d919e652-0e4a-4f12-a1f9-b18cead206e2", "path": "<TouchscreenGestureInputController>/fingerCount", "interactions": "", "processors": "", "groups": "", "action": "Screen Touch Count", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "044d4cc1-3ad7-4d38-a95f-994badd7e1a3", "path": "<TouchscreenGestureInputController>/dragDelta", "interactions": "", "processors": "", "groups": "", "action": "Drag Delta", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9e3b31e7-2f82-40f3-bfe1-ad0fa7f035d4", "path": "<TouchscreenGestureInputController>/pinchStartPosition1", "interactions": "", "processors": "", "groups": "", "action": "Pinch Start Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f3092672-a026-446d-8bb6-44843db135b1", "path": "<TouchscreenGestureInputController>/pinchGapDelta", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap Delta", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6926b069-b36d-40c2-8325-797a8deb9038", "path": "<Mouse>/scroll/y", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap Delta", "isComposite": false, "isPartOfComposite": false}, {"name": "1D Axis", "id": "da16ab02-4e2b-46f5-a969-c780423ac0e9", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap Delta", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "07794ffe-429a-49c7-93c1-83c4af6695f4", "path": "<Keyboard>/z", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap Delta", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "c480645b-97d7-4c34-8797-7f9a24edb3c5", "path": "<Keyboard>/x", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap Delta", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "0f8550ed-7261-48e0-aa0e-6670f29141f5", "path": "<TouchscreenGestureInputController>/twistStartPosition1", "interactions": "", "processors": "", "groups": "", "action": "Twist Start Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4f94c62c-7c6d-4547-82fe-b6ed10da8388", "path": "<TouchscreenGestureInputController>/pinchGap", "interactions": "", "processors": "", "groups": "", "action": "Pinch Gap", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f0f27c2a-eef2-418a-986a-811bf690fd89", "path": "<TouchscreenGestureInputController>/tapStartPosition", "interactions": "", "processors": "", "groups": "", "action": "Spawn Object", "isComposite": false, "isPartOfComposite": false}, {"name": "One Modifier", "id": "6f6c3123-8e3c-4f69-9ecc-2a605a5f8777", "path": "OneModifier", "interactions": "Tap(duration=0.5)", "processors": "", "groups": "", "action": "Spawn Object", "isComposite": true, "isPartOfComposite": false}, {"name": "modifier", "id": "ab34c73d-e0f0-4cf2-962e-2c201f9c5714", "path": "<Mouse>/press", "interactions": "", "processors": "", "groups": "", "action": "Spawn Object", "isComposite": false, "isPartOfComposite": true}, {"name": "binding", "id": "38c38fe4-fefa-4a01-a80a-6185ecb009cb", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Spawn Object", "isComposite": false, "isPartOfComposite": true}]}], "controlSchemes": []}