%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6689389171845895218
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6689389171845895219}
  - component: {fileID: 6689389171845895228}
  - component: {fileID: 6689389171845895229}
  - component: {fileID: 6689389171845895230}
  - component: {fileID: 6689389171845895231}
  - component: {fileID: 6689389171845895224}
  m_Layer: 0
  m_Name: PawnController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6689389171845895219
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.1369, y: 0, z: 0.0354}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 6689389173434678687}
  - {fileID: 8302078025757585448}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &6689389171845895228
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.03, y: 0.155, z: 0.03}
  m_Center: {x: 0, y: -0.025, z: 0}
--- !u!54 &6689389171845895229
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &6689389171845895230
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ad34abafad169848a38072baa96cdb2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_Colliders:
  - {fileID: 6689389171845895228}
  m_InteractionLayers:
    m_Bits: 1
  m_DistanceCalculationMode: 1
  m_SelectMode: 0
  m_FocusMode: 1
  m_CustomReticle: {fileID: 0}
  m_AllowGazeInteraction: 0
  m_AllowGazeSelect: 0
  m_OverrideGazeTimeToSelect: 0
  m_GazeTimeToSelect: 0.5
  m_OverrideTimeToAutoDeselectGaze: 0
  m_TimeToAutoDeselectGaze: 3
  m_AllowGazeAssistance: 0
  m_FirstHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstSelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastSelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstFocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastFocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_FocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_FocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_Activated:
    m_PersistentCalls:
      m_Calls: []
  m_Deactivated:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_StartingInteractionStrengthFilters: []
  m_AttachTransform: {fileID: 0}
  m_SecondaryAttachTransform: {fileID: 0}
  m_UseDynamicAttach: 1
  m_MatchAttachPosition: 1
  m_MatchAttachRotation: 0
  m_SnapToColliderVolume: 0
  m_ReinitializeDynamicAttachEverySingleGrab: 1
  m_AttachEaseInTime: 0
  m_MovementType: 2
  m_VelocityDamping: 1
  m_VelocityScale: 1
  m_AngularVelocityDamping: 1
  m_AngularVelocityScale: 1
  m_TrackPosition: 1
  m_SmoothPosition: 1
  m_SmoothPositionAmount: 12
  m_TightenPosition: 0.1
  m_TrackRotation: 0
  m_SmoothRotation: 0
  m_SmoothRotationAmount: 8
  m_TightenRotation: 0.1
  m_TrackScale: 0
  m_SmoothScale: 0
  m_SmoothScaleAmount: 8
  m_TightenScale: 0.1
  m_ThrowOnDetach: 0
  m_ThrowSmoothingDuration: 0.25
  m_ThrowSmoothingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_ThrowVelocityScale: 1.5
  m_ThrowAngularVelocityScale: 1
  m_ForceGravityOnDetach: 0
  m_RetainTransformParent: 1
  m_StartingSingleGrabTransformers: []
  m_StartingMultipleGrabTransformers: []
  m_AddDefaultGrabTransformers: 1
  m_FarAttachMode: 0
--- !u!114 &6689389171845895231
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0a1302d0d134fa8a2a5b3bf4aec3c20, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PermittedDisplacementAxes: 5
  m_ConstrainedAxisDisplacementMode: 1
  m_TwoHandedRotationMode: 1
  m_AllowOneHandedScaling: 1
  m_AllowTwoHandedScaling: 0
  m_OneHandedScaleSpeed: 0.5
  m_ThresholdMoveRatioForScale: 0.05
  m_ClampScaling: 1
  m_MinimumScaleRatio: 0.25
  m_MaximumScaleRatio: 2
  m_ScaleMultiplier: 0.25
--- !u!114 &6689389171845895224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389171845895218}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 115f1a2a50d85cd4b9d6dad4c95622be, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Interactable: {fileID: 6689389171845895230}
  m_PokeCollider: {fileID: 6689389171845895228}
  m_PokeConfiguration:
    m_UseConstant: 1
    m_ConstantValue:
      m_PokeDirection: 5
      m_InteractionDepthOffset: 0
      m_EnablePokeAngleThreshold: 1
      m_PokeAngleThreshold: 45
    m_Variable: {fileID: 0}
--- !u!1 &6689389173096316706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6689389173096316707}
  - component: {fileID: 6689389173096316716}
  m_Layer: 0
  m_Name: Pawn Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6689389173096316707
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173096316706}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6689389173434678687}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6689389173096316716
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173096316706}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b3c19a119577be64885886bfcc8792fe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7696507639337603356, guid: 45b2a246bd435f74989d22078ec2752e, type: 3}
  m_Bones: []
  m_BlendShapeWeights:
  - 0
  - 0
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: -0.000000009313226, y: 0.026900887, z: 0}
    m_Extent: {x: 0.012847318, y: 0.026900887, z: 0.0128473295}
  m_DirtyAABB: 0
--- !u!1 &6689389173443419733
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6689389173434678687}
  - component: {fileID: 6689389173434678681}
  - component: {fileID: 6689389173002939785}
  - component: {fileID: 6689389173002939784}
  - component: {fileID: 5052028028641032844}
  m_Layer: 0
  m_Name: Chess Pawn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6689389173434678687
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173443419733}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6689389173096316707}
  m_Father: {fileID: 6689389171845895219}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6689389173434678681
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173443419733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 49e0a5b5ff5540f5b14dd29d46faec22, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractableSource: {fileID: 6689389171845895230}
  m_IgnoreHoverEvents: 0
  m_IgnoreHoverPriorityEvents: 1
  m_IgnoreFocusEvents: 1
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 0
  m_SelectClickAnimationMode: 0
  m_ActivateClickAnimationMode: 0
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!114 &6689389173002939785
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173443419733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 6689389173096316716}
  m_MaterialIndex: 0
--- !u!114 &6689389173002939784
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173443419733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 6689389173434678681}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: c39d3614cd6089347a01b0743003ae34, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 6689389173002939785}
  m_ColorPropertyName: _BaseColor
--- !u!114 &5052028028641032844
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6689389173443419733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78373ff495dc8234887e0cbdc7140022, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PokeFilter: {fileID: 6689389171845895224}
  m_SkinnedMeshRenderer: {fileID: 6689389173096316716}
  m_BlendShapeIndex: 1
  m_BlendShapeMin: 0
  m_BlendShapeMax: 100
--- !u!1001 &490287090769008218
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 6689389171845895219}
    m_Modifications:
    - target: {fileID: 1515492005369169331, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_Name
      value: AudioAffordance
      objectReference: {fileID: 0}
    - target: {fileID: 5746416808058614602, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_AffordanceStateProvider
      value: 
      objectReference: {fileID: 6689389173434678681}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
--- !u!4 &8302078025757585448 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8501457409089496690, guid: 1c54cdd9a14d96049a8d37d422bd0e17, type: 3}
  m_PrefabInstance: {fileID: 490287090769008218}
  m_PrefabAsset: {fileID: 0}
