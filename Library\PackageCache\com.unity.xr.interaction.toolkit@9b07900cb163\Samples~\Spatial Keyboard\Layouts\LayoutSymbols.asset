%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 944d8cf1c59c0e8468bcd7e2fd86fe4d, type: 3}
  m_Name: LayoutSymbols
  m_EditorClassIdentifier: 
  m_DefaultKeyFunction: {fileID: 11400000, guid: 5e3d82ae0a9ac0d43ada00a218daeefd, type: 2}
  m_KeyMappings:
  - m_Character: '-'
    m_DisplayCharacter: '-'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: /
    m_DisplayCharacter: /
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ':'
    m_DisplayCharacter: ':'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ;
    m_DisplayCharacter: ;
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: (
    m_DisplayCharacter: (
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: )
    m_DisplayCharacter: )
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: $
    m_DisplayCharacter: $
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '&'
    m_DisplayCharacter: '&'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '@'
    m_DisplayCharacter: '@'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '"'
    m_DisplayCharacter: '"'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '['
    m_DisplayCharacter: '['
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ']'
    m_DisplayCharacter: ']'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '{'
    m_DisplayCharacter: '{'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '}'
    m_DisplayCharacter: '}'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '#'
    m_DisplayCharacter: '#'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '%'
    m_DisplayCharacter: '%'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ^
    m_DisplayCharacter: ^
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '*'
    m_DisplayCharacter: '*'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: +
    m_DisplayCharacter: +
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: =
    m_DisplayCharacter: =
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: _
    m_DisplayCharacter: _
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: \
    m_DisplayCharacter: \
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '|'
    m_DisplayCharacter: '|'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ~
    m_DisplayCharacter: ~
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: <
    m_DisplayCharacter: <
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '>'
    m_DisplayCharacter: '>'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: "\u20AC"
    m_DisplayCharacter: "\u20AC"
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: "\xA3"
    m_DisplayCharacter: "\xA3"
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: "\xA5"
    m_DisplayCharacter: "\xA5"
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: \s
    m_DisplayCharacter: shift
    m_DisplayIcon: {fileID: 373736265, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_ShiftCharacter: \s
    m_ShiftDisplayCharacter: shift
    m_ShiftDisplayIcon: {fileID: 373736265, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_OverrideDefaultKeyFunction: 1
    m_KeyFunction: {fileID: 11400000, guid: fd75c3033834cc447bf5886f0424c0b9, type: 2}
    m_KeyCode: 304
    m_Disabled: 1
  - m_Character: .
    m_DisplayCharacter: .
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ','
    m_DisplayCharacter: ','
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '?'
    m_DisplayCharacter: '?'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '!'
    m_DisplayCharacter: '!'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ''''
    m_DisplayCharacter: ''''
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: "\u2022"
    m_DisplayCharacter: "\u2022"
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: '`'
    m_DisplayCharacter: '`'
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: 
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: \h
    m_DisplayCharacter: Hide
    m_DisplayIcon: {fileID: 1163011659, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_ShiftCharacter: \h
    m_ShiftDisplayCharacter: Hide
    m_ShiftDisplayIcon: {fileID: 1163011659, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_OverrideDefaultKeyFunction: 1
    m_KeyFunction: {fileID: 11400000, guid: fafb46c355cbf0c4ab314558d216a90f, type: 2}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: \e
    m_DisplayCharacter: 
    m_DisplayIcon: {fileID: 1365469357, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_ShiftCharacter: \e
    m_ShiftDisplayCharacter: 
    m_ShiftDisplayIcon: {fileID: 1365469357, guid: e6a95303640ef3c4188ac4583185f5ba, type: 3}
    m_OverrideDefaultKeyFunction: 1
    m_KeyFunction: {fileID: 11400000, guid: abe6f1e7debe4b842a92014211d944fb, type: 2}
    m_KeyCode: 0
    m_Disabled: 1
  - m_Character: \sym
    m_DisplayCharacter: ABC
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: \sym
    m_ShiftDisplayCharacter: ABC
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 1
    m_KeyFunction: {fileID: 11400000, guid: abe6f1e7debe4b842a92014211d944fb, type: 2}
    m_KeyCode: 0
    m_Disabled: 0
  - m_Character: ' '
    m_DisplayCharacter: Space
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: ' '
    m_ShiftDisplayCharacter: Space
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 0
    m_KeyFunction: {fileID: 0}
    m_KeyCode: 32
    m_Disabled: 0
  - m_Character: \r
    m_DisplayCharacter: Enter
    m_DisplayIcon: {fileID: 0}
    m_ShiftCharacter: \r
    m_ShiftDisplayCharacter: Enter
    m_ShiftDisplayIcon: {fileID: 0}
    m_OverrideDefaultKeyFunction: 1
    m_KeyFunction: {fileID: 11400000, guid: 2ff02eb02547eaf41855aabb5c603f8b, type: 2}
    m_KeyCode: 13
    m_Disabled: 0
