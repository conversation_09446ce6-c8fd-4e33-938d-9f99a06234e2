%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1929406674756097472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5909398001603780732}
  - component: {fileID: 6324184579401567070}
  m_Layer: 0
  m_Name: HandMenuRig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5909398001603780732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1929406674756097472}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4785593887994352529}
  - {fileID: 6560354495274216278}
  - {fileID: 5310702087570344806}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6324184579401567070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1929406674756097472}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aceb12f22b4c45e59443ac967a8ece7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HandMenuUIGameObject: {fileID: 3457113661323325636}
  m_MenuHandedness: 3
  m_HandMenuUpDirection: 1
  m_LeftPalmAnchor: {fileID: 4785593887994352529}
  m_RightPalmAnchor: {fileID: 6560354495274216278}
  m_MinFollowDistance: 0.005
  m_MaxFollowDistance: 0.03
  m_MinToMaxDelaySeconds: 1
  m_HideMenuWhenGazeDiverges: 1
  m_MenuVisibleGazeAngleDivergenceThreshold: 35
  m_AnimateMenuHideAndReveal: 1
  m_RevealHideAnimationDuration: 0.1
  m_HideMenuOnSelect: 1
  m_InteractionManager: {fileID: 0}
  m_HandTrackingFollowPreset:
    m_UseConstant: 0
    m_ConstantValue:
      rightHandLocalPosition: {x: 0, y: 0, z: 0}
      leftHandLocalPosition: {x: 0, y: 0, z: 0}
      rightHandLocalRotation: {x: 0, y: 0, z: 0}
      leftHandLocalRotation: {x: 0, y: 0, z: 0}
      palmReferenceAxis: 0
      invertAxisForRightHand: 0
      requirePalmFacingUser: 0
      palmFacingUserDegreeAngleThreshold: 0
      requirePalmFacingUp: 0
      palmFacingUpDegreeAngleThreshold: 0
      snapToGaze: 0
      snapToGazeAngleThreshold: 0
      hideDelaySeconds: 0.25
      allowSmoothing: 1
      followLowerSmoothingValue: 12
      followUpperSmoothingValue: 16
    m_Variable: {fileID: 11400000, guid: 15088c60ea1e00448a95ebaef96316da, type: 2}
  m_ControllerFollowPreset:
    m_UseConstant: 0
    m_ConstantValue:
      rightHandLocalPosition: {x: 0, y: 0, z: 0}
      leftHandLocalPosition: {x: 0, y: 0, z: 0}
      rightHandLocalRotation: {x: 0, y: 0, z: 0}
      leftHandLocalRotation: {x: 0, y: 0, z: 0}
      palmReferenceAxis: 0
      invertAxisForRightHand: 0
      requirePalmFacingUser: 0
      palmFacingUserDegreeAngleThreshold: 0
      requirePalmFacingUp: 0
      palmFacingUpDegreeAngleThreshold: 0
      snapToGaze: 0
      snapToGazeAngleThreshold: 0
      hideDelaySeconds: 0.25
      allowSmoothing: 1
      followLowerSmoothingValue: 12
      followUpperSmoothingValue: 16
    m_Variable: {fileID: 11400000, guid: 2899508b1645c5e4fa421b4217da9539, type: 2}
--- !u!1 &2754306322374449658
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4785593887994352529}
  - component: {fileID: 9048520510989600354}
  m_Layer: 0
  m_Name: Left Hand Tracked Anchor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4785593887994352529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2754306322374449658}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5909398001603780732}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9048520510989600354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2754306322374449658}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 1
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position Input
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: 8eca31ce-af94-4489-93af-98c8dd23d3d5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -2024308242397127297, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation Input
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 8683248d-dc08-4118-bced-10787ad11fb2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 8248158260566104461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State Input
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 9e7f60d6-fc72-48c4-959a-c80ad3b02565
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 684395432459739428, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
--- !u!1 &3457113661323325636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5310702087570344806}
  m_Layer: 0
  m_Name: Follow GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5310702087570344806
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3457113661323325636}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5909398001603780732}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5997865275637665325
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6560354495274216278}
  - component: {fileID: 6146648593719566879}
  m_Layer: 0
  m_Name: Right Hand Tracked Anchor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6560354495274216278
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5997865275637665325}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5909398001603780732}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6146648593719566879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5997865275637665325}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 1
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position Input
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: 8eca31ce-af94-4489-93af-98c8dd23d3d5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -3326005586356538449, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation Input
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 8683248d-dc08-4118-bced-10787ad11fb2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 5101698808175986029, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State Input
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 20198b68-d65f-43a7-a5dd-66a9a1e0df0e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -1277054153949319361, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
