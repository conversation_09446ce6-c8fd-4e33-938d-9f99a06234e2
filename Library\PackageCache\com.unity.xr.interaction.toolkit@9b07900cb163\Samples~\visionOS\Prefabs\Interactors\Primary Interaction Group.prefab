%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3544334704882757637
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5650371729256867415}
  - component: {fileID: 7716237170154014286}
  m_Layer: 0
  m_Name: Primary Interaction Group
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5650371729256867415
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3544334704882757637}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8380754368985044326}
  - {fileID: 6071557487747322402}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7716237170154014286
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3544334704882757637}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: Primary Interactors
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 4795276170761438011}
  - {fileID: 3506684427908219988}
  m_StartingInteractionOverridesMap:
  - groupMember: {fileID: 4795276170761438011}
    overrideGroupMembers:
    - {fileID: 3506684427908219988}
--- !u!1 &8036844159190807011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6071557487747322402}
  - component: {fileID: 3506684427908219988}
  - component: {fileID: 8146842563655439879}
  - component: {fileID: 7203450973641461857}
  - component: {fileID: 8633789960913785040}
  - component: {fileID: 1078530018020178498}
  m_Layer: 0
  m_Name: Primary NearFarInteractor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6071557487747322402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5650371729256867415}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3506684427908219988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 25a07ef133a37d140a87cdf1f1c75fdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 2
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 3
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 0100c473-ca6c-4b47-b27d-91676105fe7c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: f8ac4c21-371b-4b9b-a5db-73d6828456ac
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 1078530018020178498}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 0b5f5371-dc47-4ac1-8e44-f7af158c72aa
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d36820c6-7ccb-4768-8601-42a7aed2af73
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_InteractionAttachController: {fileID: 8146842563655439879}
  m_EnableNearCasting: 1
  m_NearInteractionCaster: {fileID: 7203450973641461857}
  m_NearCasterSortingStrategy: 1
  m_SortNearTargetsAfterTargetFilter: 0
  m_EnableFarCasting: 1
  m_FarInteractionCaster: {fileID: 8633789960913785040}
  m_FarAttachMode: 1
  m_EnableUIInteraction: 0
  m_BlockUIOnInteractableSelection: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_UIPressInput:
    m_InputSourceMode: 3
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: a8593df8-0188-47c2-b577-6735e375b1c5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 5061e094-967b-4904-a0ef-e53a842692e4
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 1078530018020178498}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: e8e0aded-8db3-487f-baed-890a75c8ecda
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!114 &8146842563655439879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 792f6c7eaa1a4b82abf8351559ac97eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransformToFollow: {fileID: 6071557487747322402}
  m_MotionStabilizationMode: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_SmoothOffset: 1
  m_SmoothingSpeed: 10
  m_UseDistanceBasedVelocityScaling: 1
  m_UseMomentum: 1
  m_MomentumDecayScale: 1.25
  m_ZVelocityRampThreshold: 0.25
  m_PullVelocityBias: 0.5
  m_PushVelocityBias: 0.25
  m_MinAdditionalVelocityScalar: 0
  m_MaxAdditionalVelocityScalar: 0
--- !u!114 &7203450973641461857
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43127b12dccfa49178c38789c0a533d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 6071557487747322402}
  m_EnableStabilization: 0
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 0}
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_PhysicsTriggerInteraction: 1
  m_CastRadius: 0.025
--- !u!114 &8633789960913785040
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 98fb8d74eea654fe88c98f5f0877dab7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 6071557487747322402}
  m_EnableStabilization: 0
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 0}
  m_SpatialTouchInputReader: {fileID: 1078530018020178498}
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_PhysicsTriggerInteraction: 1
--- !u!114 &1078530018020178498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8036844159190807011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c3cb88545152a4bf897ea991d1f6d05d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsPrimaryTouch: 1
  m_ReleaseTimeOutDelay: 0.1
  m_SupportedPointerKind: 6
--- !u!1 &8659941920103972450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8380754368985044326}
  - component: {fileID: 4795276170761438011}
  - component: {fileID: 1184214606051688261}
  - component: {fileID: 3723606710575393904}
  m_Layer: 0
  m_Name: Primary Poke
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8380754368985044326
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8659941920103972450}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5650371729256867415}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4795276170761438011
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8659941920103972450}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0924bcaa9eb50df458a783ae0e2b59f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 0
  m_AttachTransform: {fileID: 8380754368985044326}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_PokeDepth: 0.1
  m_PokeWidth: 0.0075
  m_PokeSelectWidth: 0.015
  m_PokeHoverRadius: 0.015
  m_PokeInteractionOffset: 0.005
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_PhysicsTriggerInteraction: 1
  m_RequirePokeFilter: 1
  m_EnableUIInteraction: 0
  m_DebugVisualizationsEnabled: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1184214606051688261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8659941920103972450}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 65100e621aef34c14b213661e9366861, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PokeInteractor: {fileID: 4795276170761438011}
  m_SpatialTouchInputReader: {fileID: 3723606710575393904}
--- !u!114 &3723606710575393904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8659941920103972450}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c3cb88545152a4bf897ea991d1f6d05d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsPrimaryTouch: 1
  m_ReleaseTimeOutDelay: 0.1
  m_SupportedPointerKind: 1
