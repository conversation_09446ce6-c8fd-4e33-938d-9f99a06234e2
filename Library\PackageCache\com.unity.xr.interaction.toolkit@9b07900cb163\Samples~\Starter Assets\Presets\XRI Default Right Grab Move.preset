%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Right Grab Move
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: 8b94c4c83dec6a94fbaebf543478259e, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Mediator
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TransformationPriority
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableFreeXMovement
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableFreeYMovement
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableFreeZMovement
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UseGravity
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GravityApplicationMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ControllerTransform
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableMoveWhileSelecting
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MoveFactor
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Name
    value: Grab Move
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Id
    value: 67220c99-f046-4e98-aa6f-d84114cad173
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionPerformed.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Name
    value: Grab Move Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Id
    value: ed114d26-3fbf-41fc-80fa-9675240038c5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionValue.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionReferencePerformed
    value: 
    objectReference: {fileID: 15759602096507913, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_InputActionReferenceValue
    value: 
    objectReference: {fileID: 15759602096507913, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualPerformed
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualValue
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualQueuePerformed
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualQueueWasPerformedThisFrame
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualQueueValue
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveInput.m_ManualQueueTargetFrame
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Name
    value: Grab Move
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Id
    value: de56d195-bf90-4347-9982-6bf8ffa3420c
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_GrabMoveAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
