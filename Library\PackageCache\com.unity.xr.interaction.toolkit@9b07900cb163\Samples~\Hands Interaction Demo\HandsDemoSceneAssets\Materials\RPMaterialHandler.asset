%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7883133e628dff4a86f50c082f77055, type: 3}
  m_Name: RPMaterialHandler
  m_EditorClassIdentifier: 
  m_ShaderContainers:
  - material: {fileID: 2100000, guid: 54f7c59213ac02b4d9f00104348dbac3, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 6c72d2d2ead00e54db4b71b27ffb03d4, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 40808a1b89c64184e9466aaec1fd1ac1, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 53c16fb5d5c516b40a1f6a8fc34132f3, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: f95c205049f12cc4cb5bd57dbded286c, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: 2edb876ca20f6ea40b4ebb61f96f1df1, type: 2}
    useSRPShaderName: 1
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: 0}
    useBuiltinShaderName: 1
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 0}
  - material: {fileID: 2100000, guid: b3c19a119577be64885886bfcc8792fe, type: 2}
    useSRPShaderName: 0
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: 0927d29e476ce5843b1f7d2a96943c51, type: 3}
    useBuiltinShaderName: 0
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  m_AutoRefreshShaders: 1
