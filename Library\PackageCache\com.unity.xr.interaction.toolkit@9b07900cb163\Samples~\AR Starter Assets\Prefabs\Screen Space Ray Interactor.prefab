%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1925633590846275013
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1382283371425239657}
  - component: {fileID: 5124318107472014488}
  m_Layer: 0
  m_Name: Rotate Input
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1382283371425239657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925633590846275013}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6605111590819766859}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5124318107472014488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925633590846275013}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8552f8756b2d288408fd498c09521d36, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RayInteractor: {fileID: 6605111590819766852}
  m_TwistDeltaRotationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Twist Delta Rotation
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 0d0dc30d-730a-403c-a15e-e569994adae8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8156239294363760665, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_DragDeltaInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Drag Delta
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: a14680a7-d542-4505-aef9-4338d6bbd4a1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -3603844561257126198, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScreenTouchCountInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Screen Touch Count
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 1409435d-da38-4a1e-b22a-32002b13df89
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 4162966010302970412, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!1 &5944071432219833673
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8052380932886628051}
  - component: {fileID: 3656531940955049870}
  m_Layer: 0
  m_Name: Select Input
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8052380932886628051
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5944071432219833673}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6605111590819766859}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3656531940955049870
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5944071432219833673}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f4dc779781245c4fb67485037f7c563, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TapStartPositionInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Tap Start Position
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 61a055a4-3143-4933-811e-5f0e7d0e25a4
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 2494954584338170553, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DragCurrentPositionInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Drag Current Position
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: de616a0d-b656-42b1-9466-38b70e50155d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -7530398834462728267, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_PinchGapDeltaInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Pinch Gap Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 0cb90ffe-8281-4c67-b2c6-a01a53915a3c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -5112888916153672211, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_TwistDeltaRotationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Twist Delta Rotation
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 128b79a7-92af-4156-8f46-79aca4634514
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8156239294363760665, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!1 &6514665646172456093
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4061769689837644284}
  - component: {fileID: 6746537367369682168}
  m_Layer: 0
  m_Name: Scale Distance Delta Input
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4061769689837644284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6514665646172456093}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6605111590819766859}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6746537367369682168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6514665646172456093}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14264165a1650b54a9900cc12af9cec1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UseRotationThreshold: 1
  m_RotationThreshold: 0.02
  m_PinchGapDeltaInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Pinch Gap Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 213e6d0f-f5b5-4479-b35d-564db0a8e1b5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -5112888916153672211, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_TwistDeltaRotationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Twist Delta Rotation
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 67b7eada-a58b-48f6-99ab-5cc49843b41a
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8156239294363760665, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!1 &6605111590819766858
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6605111590819766859}
  - component: {fileID: 6076859148723705966}
  - component: {fileID: 6605111590819766852}
  - component: {fileID: 2427033034031412199}
  - component: {fileID: 7852493624469577550}
  - component: {fileID: 774100038497631397}
  m_Layer: 0
  m_Name: Screen Space Ray Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6605111590819766859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 8052380932886628051}
  - {fileID: 1382283371425239657}
  - {fileID: 4061769689837644284}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6076859148723705966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1aafed8f78cf16469d903184f65d9a6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TapDuration: 0.5
--- !u!114 &6605111590819766852
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters:
  - {fileID: 7852493624469577550}
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 3
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 33ced78a-0a16-46be-a821-38e76e95aa58
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 69214b20-9ca9-4d67-a036-10ad43cad668
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 3656531940955049870}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 1d685e23-1439-4692-a81d-3339ff63f475
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: a1a435a9-b4b7-42f9-9edd-712552a40743
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_LineType: 0
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 0}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 16
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 20
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_HitClosestOnly: 0
  m_HoverToSelect: 0
  m_HoverTimeToSelect: 0.5
  m_AutoDeselect: 0
  m_TimeToAutoDeselect: 3
  m_EnableUIInteraction: 1
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 1
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 0
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 1
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 1
  m_ScaleMode: 2
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: a2136a80-e29b-4868-9a9b-42c496cb2d4d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 6827ce31-bbbd-4e55-b387-eb6be957f8b2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: b8ee2c7e-1ad4-4c36-bcf0-63468da0f553
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 87bbe0d5-0b68-4d7d-8aac-197c5578b3a9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 3
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 43159ac4-4a30-4125-bba3-97192c4c28df
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 5124318107472014488}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: d1b8339c-119b-403a-8e4e-5ca86e19fdf8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 6c314288-307f-4225-88c4-c6638dad639d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 2f0af966-3359-40ff-9a62-53c023bf865d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 3efb5667-5a54-4070-a325-a3ab11bd3af7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 3
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: dd303425-9720-4874-b54d-30f33c8c44d9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 6746537367369682168}
    m_ManualValue: 0
--- !u!114 &2427033034031412199
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: 
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 6605111590819766852}
  m_StartingInteractionOverridesMap: []
--- !u!114 &7852493624469577550
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd8a59f6efffdc408e9e61fc03c1417, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ScreenTouchCountInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Screen Touch Count
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: b3adeafb-4841-46ec-8830-8463242d25a4
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 4162966010302970412, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!114 &774100038497631397
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6605111590819766858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d08dffc5b169f040870b8f1bfe27403, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ControllerCamera: {fileID: 0}
  m_TapStartPositionInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Tap Start Position
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 8a2cc121-2dbc-42fe-adaf-622ea2b3d171
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 2494954584338170553, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DragStartPositionInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Drag Start Position
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 506a8457-08f4-43bc-8055-ea8d2f50f049
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -646570392816163950, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DragCurrentPositionInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Drag Current Position
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 0c6d1240-4ee9-49af-a967-2247bb090258
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -7530398834462728267, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScreenTouchCountInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Screen Touch Count
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: b79acf2d-23bd-4013-83fd-f356516054a2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 4162966010302970412, guid: c348712bda248c246b8c49b3db54643f, type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
