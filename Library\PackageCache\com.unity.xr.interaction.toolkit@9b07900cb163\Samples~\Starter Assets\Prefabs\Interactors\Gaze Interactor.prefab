%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3055433562365713971
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7378618157167557198}
  - component: {fileID: 6766910295942714439}
  - component: {fileID: 6161168854630649507}
  - component: {fileID: 2894763562165408636}
  m_Layer: 0
  m_Name: Gaze Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7378618157167557198
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3055433562365713971}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6766910295942714439
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3055433562365713971}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c416f1a5c494e224fb5564fd1362b50d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 26dcb486-2cd5-4bf0-83a4-8252a6419ca1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d589f510-c88e-41dc-89ee-4accd74ded87
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 1b75ac5e-63d2-4c5c-9b86-0fe382e6b137
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: fde38f1d-c7ff-4233-b8ba-2548488943d7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 0
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 0
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 0
  m_LineType: 0
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 0}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 16
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 20
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 0
  m_HitClosestOnly: 0
  m_HoverToSelect: 1
  m_HoverTimeToSelect: 1
  m_AutoDeselect: 1
  m_TimeToAutoDeselect: 0.25
  m_EnableUIInteraction: 1
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 0
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 1
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 0
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 0
  m_ScaleMode: 0
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 8e16e22c-3195-4e76-b0d2-9ec60d8bfc8e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: f2af0d9f-965a-4778-accc-36828a6e40b8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 83ec219e-cbbd-4b69-9013-f330cea06247
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 78fd2d8b-06b9-4583-8d46-a896cee22152
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 2941b9e4-5f6c-48d5-9338-136ff3a60e62
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 4decd4bd-22c4-4e97-af9b-f22d09a3ea8e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 4e7a5ca5-86e4-4d9d-9678-01fb7083e39b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 0c82215d-82dc-4f94-a5a2-cd3a7186171a
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: ec6d1bcc-90c1-4e80-98a9-7de37426ef90
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 7e170bb1-3339-4b96-bbd9-61c01cb414db
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_GazeAssistanceCalculation: 1
  m_GazeAssistanceColliderFixedSize: 1
  m_GazeAssistanceColliderScale: 1
  m_GazeAssistanceSnapVolume: {fileID: 0}
  m_GazeAssistanceDistanceScaling: 0
  m_ClampGazeAssistanceDistanceScaling: 0
  m_GazeAssistanceDistanceScalingClampValue: 0
--- !u!114 &6161168854630649507
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3055433562365713971}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6ef0e4723b64c884699a375196c13ac0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FallbackIfEyeTrackingUnavailable: 1
--- !u!114 &2894763562165408636
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3055433562365713971}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: e0974d43-a211-4251-882f-3f0b4749db16
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 3220680263695665919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 0a88b07c-2048-460b-b52d-880dd98ceb35
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5930349909990434036, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: ca2485cb-f4d4-4bef-84e6-b085e080175c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 2069149553511882089, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: b902054d-edbb-440a-a455-68558ef17b58
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 629180f1-a17a-4e47-a583-481808df540f
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
