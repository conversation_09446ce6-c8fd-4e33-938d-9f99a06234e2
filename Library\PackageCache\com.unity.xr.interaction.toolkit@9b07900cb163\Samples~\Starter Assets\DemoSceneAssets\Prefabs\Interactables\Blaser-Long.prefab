%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3385772945386142710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8720395994140980207}
  m_Layer: 0
  m_Name: Attach 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8720395994140980207
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3385772945386142710}
  m_LocalRotation: {x: 0.5, y: -0.5, z: 0.5, w: 0.5}
  m_LocalPosition: {x: 0.05, y: 0.04, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1511484438512298306}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 90}
--- !u!1 &6437316271001355229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5917203958825261415}
  - component: {fileID: 7327913166118033090}
  - component: {fileID: 4779741401209332232}
  - component: {fileID: 8555559421212654628}
  - component: {fileID: 4302118958288390173}
  m_Layer: 0
  m_Name: Blaster_Long
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5917203958825261415
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6437316271001355229}
  m_LocalRotation: {x: 0.000000081460335, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5354890054544157949}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7327913166118033090
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6437316271001355229}
  m_Mesh: {fileID: -4545305392863491524, guid: e51d3dbfe79e4c646bb30424a11f23a0, type: 3}
--- !u!23 &4779741401209332232
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6437316271001355229}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 76618f7490c40334fa7b685859587d2e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &8555559421212654628
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6437316271001355229}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.07774649
  m_Height: 0
  m_Direction: 1
  m_Center: {x: 0.05, y: 0, z: -0.000000004656613}
--- !u!65 &4302118958288390173
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6437316271001355229}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.06, y: 0.35081995, z: 0.060000017}
  m_Center: {x: 0, y: -0.10121217, z: -0.000000004656613}
--- !u!1 &6910721658033247306
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5354890054544157949}
  - component: {fileID: 7469549042548629770}
  - component: {fileID: 5007766103700882973}
  m_Layer: 0
  m_Name: Blaser-Long
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5354890054544157949
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6910721658033247306}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.939, y: 1.965, z: -4.572}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5917203958825261415}
  - {fileID: 1511484438512298306}
  - {fileID: 1847048393024268602}
  - {fileID: 3152469075122894703}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7469549042548629770
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6910721658033247306}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 1
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5007766103700882973
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6910721658033247306}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ad34abafad169848a38072baa96cdb2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_Colliders: []
  m_InteractionLayers:
    m_Bits: 1
  m_DistanceCalculationMode: 1
  m_SelectMode: 1
  m_FocusMode: 1
  m_CustomReticle: {fileID: 0}
  m_AllowGazeInteraction: 0
  m_AllowGazeSelect: 0
  m_OverrideGazeTimeToSelect: 0
  m_GazeTimeToSelect: 0.5
  m_OverrideTimeToAutoDeselectGaze: 0
  m_TimeToAutoDeselectGaze: 3
  m_AllowGazeAssistance: 0
  m_FirstHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstSelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastSelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_FirstFocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_LastFocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_FocusEntered:
    m_PersistentCalls:
      m_Calls: []
  m_FocusExited:
    m_PersistentCalls:
      m_Calls: []
  m_Activated:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3152469075122894700}
        m_TargetAssemblyTypeName: UnityEngine.ParticleSystem, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  m_Deactivated:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_StartingInteractionStrengthFilters: []
  m_AttachTransform: {fileID: 8720395994140980207}
  m_SecondaryAttachTransform: {fileID: 8163488654232436768}
  m_UseDynamicAttach: 0
  m_MatchAttachPosition: 1
  m_MatchAttachRotation: 1
  m_SnapToColliderVolume: 1
  m_ReinitializeDynamicAttachEverySingleGrab: 1
  m_AttachEaseInTime: 0.15
  m_MovementType: 2
  m_VelocityDamping: 1
  m_VelocityScale: 1
  m_AngularVelocityDamping: 1
  m_AngularVelocityScale: 1
  m_TrackPosition: 1
  m_SmoothPosition: 0
  m_SmoothPositionAmount: 8
  m_TightenPosition: 0.1
  m_TrackRotation: 1
  m_SmoothRotation: 0
  m_SmoothRotationAmount: 8
  m_TightenRotation: 0.1
  m_TrackScale: 1
  m_SmoothScale: 0
  m_SmoothScaleAmount: 8
  m_TightenScale: 0.1
  m_ThrowOnDetach: 1
  m_ThrowSmoothingDuration: 0.25
  m_ThrowSmoothingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_ThrowVelocityScale: 1.5
  m_ThrowAngularVelocityScale: 1
  m_ForceGravityOnDetach: 0
  m_RetainTransformParent: 1
  m_StartingSingleGrabTransformers: []
  m_StartingMultipleGrabTransformers: []
  m_AddDefaultGrabTransformers: 1
  m_FarAttachMode: 1
--- !u!1 &7408197437310189400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1511484438512298306}
  m_Layer: 0
  m_Name: Attach Transforms
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1511484438512298306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7408197437310189400}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8720395994140980207}
  - {fileID: 8163488654232436768}
  m_Father: {fileID: 5354890054544157949}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8124602369512158954
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8163488654232436768}
  m_Layer: 0
  m_Name: Attach 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8163488654232436768
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8124602369512158954}
  m_LocalRotation: {x: 0.5, y: -0.5, z: 0.5, w: 0.5}
  m_LocalPosition: {x: 0.0025, y: -0.15, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1511484438512298306}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 90}
--- !u!1001 &88085993893310710
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5354890054544157949}
    m_Modifications:
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.284
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3064453622967830428, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
      propertyPath: m_Name
      value: Confetti
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
--- !u!198 &3152469075122894700 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 3064453622967830426, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
  m_PrefabInstance: {fileID: 88085993893310710}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &3152469075122894703 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3064453622967830425, guid: 7942e6544a2b2ae48bcf988d9aed838d, type: 3}
  m_PrefabInstance: {fileID: 88085993893310710}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5862368334091776017
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5354890054544157949}
    m_Modifications:
    - target: {fileID: 3774509235512974894, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Name
      value: Highlight Interaction Affordance
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7396278978564332023, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_Renderer
      value: 
      objectReference: {fileID: 4779741401209332232}
    - target: {fileID: 8634317094661461186, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
      propertyPath: m_InteractableSource
      value: 
      objectReference: {fileID: 5007766103700882973}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
--- !u!4 &1847048393024268602 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5258331117553129771, guid: 6b12f432fa58c224baf0d659706362be, type: 3}
  m_PrefabInstance: {fileID: 5862368334091776017}
  m_PrefabAsset: {fileID: 0}
