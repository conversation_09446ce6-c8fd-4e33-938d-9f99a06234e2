using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Editor")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Tests")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Editor.Tests")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Samples.StarterAssets")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Samples.ARStarterAssets")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Samples.Hands")]
[assembly: InternalsVisibleTo("Unity.XR.Interaction.Toolkit.Samples.VisionOS")]
