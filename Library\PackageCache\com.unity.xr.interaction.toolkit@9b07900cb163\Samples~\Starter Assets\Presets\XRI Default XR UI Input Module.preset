%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default XR UI Input Module
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: ab68ce6587aab0146b8dabefbd806791, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SendPointerHoverToParent
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ClickSpeed
    value: 0.3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MoveDeadzone
    value: 0.6
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RepeatDelay
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RepeatRate
    value: 0.1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackedDeviceDragThresholdMultiplier
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackedScrollDeltaMultiplier
    value: 5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActiveInputMode
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableXRInput
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableMouseInput
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTouchInput
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PointAction
    value: 
    objectReference: {fileID: 2869410428622933342, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_LeftClickAction
    value: 
    objectReference: {fileID: 1855836014308820768, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_MiddleClickAction
    value: 
    objectReference: {fileID: -6289560987278519447, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightClickAction
    value: 
    objectReference: {fileID: -2562941478296515153, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScrollWheelAction
    value: 
    objectReference: {fileID: 5825226938762934180, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_NavigateAction
    value: 
    objectReference: {fileID: -7967456002180160679, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SubmitAction
    value: 
    objectReference: {fileID: 3994978066732806534, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_CancelAction
    value: 
    objectReference: {fileID: 2387711382375263438, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_EnableBuiltinActionsAsFallback
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableGamepadInput
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableJoystickInput
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HorizontalAxis
    value: Horizontal
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_VerticalAxis
    value: Vertical
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SubmitButton
    value: Submit
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CancelButton
    value: Cancel
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
