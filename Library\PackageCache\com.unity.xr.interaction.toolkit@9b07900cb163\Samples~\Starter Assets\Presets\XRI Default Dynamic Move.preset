%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Dynamic Move
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: 9b1e8c997df241c1a67045eeac79b41b, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Mediator
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TransformationPriority
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_MoveSpeed
    value: 2.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableStrafe
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableFly
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UseGravity
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ForwardSource
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Name
    value: Left Hand Move
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Id
    value: 16c2fabb-fb1c-4a11-94d0-0b1d894b8593
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_InputActionReference
    value: 
    objectReference: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMoveInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputSourceMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Name
    value: Right Hand Move
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Id
    value: af2e3d83-024e-4a1f-8bc1-f97f0b4ae1d5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputAction.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_InputActionReference
    value: 
    objectReference: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_ObjectReferenceObject
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_ManualValue.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMoveInput.m_ManualValue.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HeadTransform
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftControllerTransform
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightControllerTransform
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandMovementDirection
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandMovementDirection
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
