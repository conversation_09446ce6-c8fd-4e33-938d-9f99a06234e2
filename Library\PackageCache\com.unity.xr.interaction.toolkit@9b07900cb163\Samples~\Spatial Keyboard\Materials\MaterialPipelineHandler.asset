%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e7883133e628dff4a86f50c082f77055, type: 3}
  m_Name: MaterialPipelineHandler
  m_EditorClassIdentifier: 
  m_ShaderContainers:
  - material: {fileID: 2100000, guid: e6214d327b0cf3f4fbb4085685e38d0a, type: 2}
    useSRPShaderName: 0
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: 0927d29e476ce5843b1f7d2a96943c51, type: 3}
    useBuiltinShaderName: 0
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  - material: {fileID: 2100000, guid: 14907cbcede41d94f99fc623c76cce72, type: 2}
    useSRPShaderName: 0
    scriptableRenderPipelineShaderName: Universal Render Pipeline/Simple Lit
    scriptableRenderPipelineShader: {fileID: -6465566751694194690, guid: 0927d29e476ce5843b1f7d2a96943c51, type: 3}
    useBuiltinShaderName: 0
    builtInPipelineShaderName: Standard
    builtInPipelineShader: {fileID: 4800000, guid: b24c216c4acb0094c892a61dfbbb76b4, type: 3}
  m_AutoRefreshShaders: 1
