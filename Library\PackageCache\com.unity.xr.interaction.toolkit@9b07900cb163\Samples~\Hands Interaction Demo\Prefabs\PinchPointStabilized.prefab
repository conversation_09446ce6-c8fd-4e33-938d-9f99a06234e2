%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2181396665701132182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1517203009072151852}
  - component: {fileID: 6379762978650309333}
  - component: {fileID: 1538976526839647015}
  - component: {fileID: 8232174397725000580}
  - component: {fileID: 9205766622909133630}
  m_Layer: 0
  m_Name: Pinch Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1517203009072151852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2181396665701132182}
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 7962545760240945814}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!137 &6379762978650309333
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2181396665701132182}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f5ccd52dc494e054fbe7d7161dcabe25, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5120762275383614272, guid: e053b8fbc416ba349b4a58a26410bba2, type: 3}
  m_Bones: []
  m_BlendShapeWeights:
  - 0
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: 0, y: 0.009045093, z: 0}
    m_Extent: {x: 0.**********, y: 0.016694028, z: 0.**********}
  m_DirtyAABB: 0
--- !u!114 &1538976526839647015
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2181396665701132182}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 434cd70b6a5740cca11da2a0417cf0ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 3941121930630195656}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: idle
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hovered
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hoveredPriority
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: selected
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: activated
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: focused
        animationStateStartValue: 0
        animationStateEndValue: 0
    m_Variable: {fileID: 11400000, guid: fbf5c74e21f572740881de5634f1ce9c, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_SkinnedMeshRenderer: {fileID: 6379762978650309333}
  m_BlendShapeIndex: 0
--- !u!114 &8232174397725000580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2181396665701132182}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 865d01d2834c9cb4caa8f2c901104c2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 3941121930630195656}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: idle
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: hovered
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: hoveredPriority
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: selected
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: activated
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
      - stateName: focused
        animationStateStartValue:
          x: 0
          y: 0
          z: 0
        animationStateEndValue:
          x: 0
          y: 0
          z: 0
    m_Variable: {fileID: 11400000, guid: 2826cece951f71642a19da5a2080a27c, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_TransformToTranslate: {fileID: 1517203009072151852}
--- !u!114 &9205766622909133630
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2181396665701132182}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73feefcb6c9340e188b14a48254f4b5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 3941121930630195656}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: idle
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hovered
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hoveredPriority
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: selected
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: activated
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: focused
        animationStateStartValue: 0
        animationStateEndValue: 0
    m_Variable: {fileID: 11400000, guid: d22e48f34794ab9498d082f9713c2293, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_TransformToScale: {fileID: 1517203009072151852}
--- !u!1 &3275437478627839725
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5697399126433476533}
  m_Layer: 0
  m_Name: Pinch Visual Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5697399126433476533
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3275437478627839725}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.015}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7962545760240945814}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3369527167708781622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7962545760240945814}
  - component: {fileID: 6603267092101694357}
  - component: {fileID: 3941121930630195656}
  - component: {fileID: 868360655280004538}
  m_Layer: 0
  m_Name: PinchPointStabilized
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7962545760240945814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369527167708781622}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1517203009072151852}
  - {fileID: 5697399126433476533}
  - {fileID: 3319742158842249363}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6603267092101694357
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369527167708781622}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8693657abb5062a40a80ba3cb86ef181, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_XRHandTrackingEvents: {fileID: 0}
  m_RayInteractor: {fileID: 0}
  m_NearFarInteractor: {fileID: 0}
  m_TargetRotation: {fileID: 0}
  m_RotationSmoothingSpeed: 12
--- !u!114 &3941121930630195656
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369527167708781622}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c83f12c537584f51b92c01f10d7090c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractorSource: {fileID: 0}
  m_IgnoreHoverEvents: 0
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 1
  m_IgnoreUGUIHover: 0
  m_IgnoreUGUISelect: 0
  m_IgnoreXRInteractionEvents: 0
  m_SelectClickAnimationMode: 0
  m_ActivateClickAnimationMode: 0
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!114 &868360655280004538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369527167708781622}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e989a75b2954bdab01ca618a30d5de6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Interactor: {fileID: 0}
  m_ObjectToHide: {fileID: 2181396665701132182}
--- !u!1 &8362549039119506064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3319742158842249363}
  - component: {fileID: 5862434181995340926}
  - component: {fileID: 1545576279830946201}
  - component: {fileID: 9113310872675320958}
  m_Layer: 0
  m_Name: Material Affordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3319742158842249363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8362549039119506064}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7962545760240945814}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5862434181995340926
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8362549039119506064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 6379762978650309333}
  m_MaterialIndex: 0
--- !u!114 &1545576279830946201
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8362549039119506064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 3941121930630195656}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: fc690d1505c48cb4696838b71abd2ca0, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 5862434181995340926}
  m_ColorPropertyName: _BaseColor
--- !u!114 &9113310872675320958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8362549039119506064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 629ea686265f47f082ba5732cffad1cf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 3941121930630195656}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: idle
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hovered
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hoveredPriority
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: selected
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: activated
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: focused
        animationStateStartValue: 0
        animationStateEndValue: 0
    m_Variable: {fileID: 11400000, guid: c2712227db89c5142adad58b143bf039, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 5862434181995340926}
  m_FloatPropertyName: _RimPower
