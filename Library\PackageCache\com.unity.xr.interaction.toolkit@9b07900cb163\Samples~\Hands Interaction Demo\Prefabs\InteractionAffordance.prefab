%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2313441362174852961
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7275325890891809550}
  - component: {fileID: 60592885034133540}
  - component: {fileID: 3600643431587687517}
  m_Layer: 0
  m_Name: AudioAffordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7275325890891809550
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2313441362174852961}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 314259139610439016}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &60592885034133540
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2313441362174852961}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &3600643431587687517
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2313441362174852961}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 186056f485a2493b80cc81571ac8cd9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 1784108126610004015}
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_List:
      - stateName: disabled
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: idle
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: hovered
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: hoveredPriority
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: selected
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: activated
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
      - stateName: focused
        stateEntered: {fileID: 0}
        stateExited: {fileID: 0}
    m_Variable: {fileID: 11400000, guid: 57def9352cdee8548bfc9ebc6a55914a, type: 2}
  m_AudioSource: {fileID: 60592885034133540}
--- !u!1 &3196468547072183565
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8521807588786738958}
  - component: {fileID: 651352808372728803}
  - component: {fileID: 5558392255022446084}
  - component: {fileID: 2749842065993389027}
  m_Layer: 0
  m_Name: Material Affordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8521807588786738958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3196468547072183565}
  m_LocalRotation: {x: -0, y: -0, z: -0.00000001452281, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 314259139610439016}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &651352808372728803
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3196468547072183565}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 0}
  m_MaterialIndex: 0
--- !u!114 &5558392255022446084
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3196468547072183565}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 1784108126610004015}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: 8b0b5aede76faac438e02d2a468f4805, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 651352808372728803}
  m_ColorPropertyName: _RimColor
--- !u!114 &2749842065993389027
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3196468547072183565}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 629ea686265f47f082ba5732cffad1cf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 1784108126610004015}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: idle
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hovered
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: hoveredPriority
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: selected
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: activated
        animationStateStartValue: 0
        animationStateEndValue: 0
      - stateName: focused
        animationStateStartValue: 0
        animationStateEndValue: 0
    m_Variable: {fileID: 11400000, guid: 795305341a8dbbd46ae54e9a01d6ea95, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 651352808372728803}
  m_FloatPropertyName: _RimPower
--- !u!1 &4696973491166461409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 314259139610439016}
  - component: {fileID: 1784108126610004015}
  m_Layer: 0
  m_Name: InteractionAffordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &314259139610439016
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4696973491166461409}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7275325890891809550}
  - {fileID: 8521807588786738958}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1784108126610004015
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4696973491166461409}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 49e0a5b5ff5540f5b14dd29d46faec22, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractableSource: {fileID: 0}
  m_IgnoreHoverEvents: 0
  m_IgnoreHoverPriorityEvents: 1
  m_IgnoreFocusEvents: 1
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 0
  m_SelectClickAnimationMode: 1
  m_ActivateClickAnimationMode: 1
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
